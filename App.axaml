<Application xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialIcons="clr-namespace:Material.Icons.Avalonia;assembly=Material.Icons.Avalonia"
             xmlns:helpers="clr-namespace:Lyxie_desktop.Helpers"
             x:Class="Lyxie_desktop.App"
             RequestedThemeVariant="Default">
             <!-- "Default" ThemeVariant follows system theme variant. "Dark" or "Light" are other available options. -->

    <Application.Styles>
        <FluentTheme />
        <materialIcons:MaterialIconStyles />
    </Application.Styles>

    <Application.Resources>
        <!-- 主题颜色资源 -->
        <ResourceDictionary>
            <helpers:BoolToBrushConverter x:Key="BoolToBrushConverter" />
            <ResourceDictionary.ThemeDictionaries>
                <!-- 深色主题 -->
                <ResourceDictionary x:Key="Dark">
                    <!-- 背景颜色 -->
                    <SolidColorBrush x:Key="AppBackgroundBrush" Color="#1E1E1E"/>
                    <Color x:Key="AppBackgroundGradientStartColor">#1E1E1E</Color>
                    <Color x:Key="AppBackgroundGradientEndColor">#252526</Color>

                    <!-- 卡片/容器背景 -->
                    <SolidColorBrush x:Key="CardBackgroundBrush" Color="#2D2D30"/>
                    <SolidColorBrush x:Key="CardBorderBrush" Color="#404040"/>

                    <!-- 按钮颜色 -->
                    <SolidColorBrush x:Key="ButtonBackgroundBrush" Color="#2D2D30"/>
                    <Color x:Key="ButtonBackgroundGradientStartColor">#3C3C3C</Color>
                    <Color x:Key="ButtonBackgroundGradientEndColor">#2D2D30</Color>
                    <SolidColorBrush x:Key="ButtonBorderBrush" Color="#404040"/>
                    <Color x:Key="ButtonBorderGradientStartColor">#404040</Color>
                    <Color x:Key="ButtonBorderGradientEndColor">#505050</Color>

                    <!-- 按钮悬停状态 -->
                    <SolidColorBrush x:Key="ButtonHoverBackgroundBrush" Color="#3C3C3C"/>
                    <Color x:Key="ButtonHoverGradientStartColor">#4A4A4E</Color>
                    <Color x:Key="ButtonHoverGradientEndColor">#3E3E42</Color>
                    <Color x:Key="ButtonHoverBorderGradientStartColor">#505050</Color>
                    <Color x:Key="ButtonHoverBorderGradientEndColor">#606060</Color>

                    <!-- 按钮按下状态 - 使用主题蓝色的深色变体 -->
                    <Color x:Key="ButtonPressedGradientStartColor">#1E3A5F</Color>
                    <Color x:Key="ButtonPressedGradientEndColor">#2A4A6B</Color>

                    <!-- 半球按钮立体效果颜色 -->
                    <Color x:Key="ButtonShadowColor">#252526</Color>
                    <Color x:Key="ButtonDeepShadowColor">#1E1E1E</Color>
                    <Color x:Key="ButtonHighlightColor">#30FFFFFF</Color>
                    <Color x:Key="ButtonMidHighlightColor">#15FFFFFF</Color>

                    <!-- 3D立体按钮专用颜色 - 浅色主题 -->
                    <!-- 按钮顶面 - 最亮面，接受光照 -->
                    <Color x:Key="Button3DTopStartColor">#FFFFFF</Color>
                    <Color x:Key="Button3DTopMidColor">#F0F4F8</Color>
                    <Color x:Key="Button3DTopEndColor">#E2E8F0</Color>
                    
                    <!-- 按钮侧面 - 中等亮度，营造厚度感 -->
                    <Color x:Key="Button3DSideStartColor">#CBD5E0</Color>
                    <Color x:Key="Button3DSideEndColor">#A0AEC0</Color>
                    
                    <!-- 按钮底面/阴影 - 最暗面 -->
                    <Color x:Key="Button3DBottomColor">#718096</Color>
                    <Color x:Key="Button3DDeepShadowColor">#4A5568</Color>
                    
                    <!-- 3D按钮高光系统 -->
                    <Color x:Key="Button3DHighlightTopColor">#CCEEFFFF</Color>
                    <Color x:Key="Button3DHighlightMidColor">#80FFFFFF</Color>
                    <Color x:Key="Button3DHighlightEdgeColor">#40FFFFFF</Color>
                    
                    <!-- 3D按钮边框 -->
                    <Color x:Key="Button3DBorderTopColor">#E2E8F0</Color>
                    <Color x:Key="Button3DBorderBottomColor">#A0AEC0</Color>
                    
                    <!-- 悬停状态 -->
                    <Color x:Key="Button3DHoverTopStartColor">#F7FAFC</Color>
                    <Color x:Key="Button3DHoverTopMidColor">#EDF2F7</Color>
                    <Color x:Key="Button3DHoverTopEndColor">#E2E8F0</Color>
                    
                    <!-- 按下状态 -->
                    <Color x:Key="Button3DPressedTopColor">#CBD5E0</Color>
                    <Color x:Key="Button3DPressedBottomColor">#A0AEC0</Color>

                    <!-- 文本颜色 -->
                    <SolidColorBrush x:Key="PrimaryTextBrush" Color="#E0E0E0"/>
                    <SolidColorBrush x:Key="SecondaryTextBrush" Color="#B0B0B0"/>
                    <SolidColorBrush x:Key="AccentTextBrush" Color="#FFFFFF"/>

                    <!-- 图标颜色 -->
                    <SolidColorBrush x:Key="IconBrush" Color="#E0E0E0"/>
                    <SolidColorBrush x:Key="SecondaryIconBrush" Color="#B0B0B0"/>

                    <!-- 浮动面板 -->
                    <SolidColorBrush x:Key="FloatingPanelBackgroundBrush" Color="#CC2D2D30"/>
                    <SolidColorBrush x:Key="FloatingPanelBorderBrush" Color="#404040"/>

                    <!-- 欢迎界面背景 -->
                    <Color x:Key="WelcomeBackgroundStartColor">#000000</Color>
                    <Color x:Key="WelcomeBackgroundEndColor">#1A1A1A</Color>

                    <!-- 光晕效果颜色 -->
                    <Color x:Key="GlowPrimaryColor">#4A9EFF</Color>
                    <Color x:Key="GlowSecondaryColor">#7B68EE</Color>
                    <Color x:Key="GlowAccentColor">#20B2AA</Color>
                    <SolidColorBrush x:Key="GlowPrimaryBrush" Color="{DynamicResource GlowPrimaryColor}"/>
                    <SolidColorBrush x:Key="GlowSecondaryBrush" Color="{DynamicResource GlowSecondaryColor}"/>
                    <SolidColorBrush x:Key="GlowAccentBrush" Color="{DynamicResource GlowAccentColor}"/>

                    <!-- 词云文字颜色 -->
                    <SolidColorBrush x:Key="WordCloudTextBrush" Color="#80FFFFFF"/>

                    <!-- 对话界面颜色 -->
                    <SolidColorBrush x:Key="ChatBackgroundBrush" Color="#252526"/>
                    <SolidColorBrush x:Key="ChatHeaderBackgroundBrush" Color="#2D2D30"/>
                    <SolidColorBrush x:Key="ChatInputBackgroundBrush" Color="#2D2D30"/>
                    <SolidColorBrush x:Key="PrimaryButtonBackgroundBrush" Color="#0078D4"/>
                    <SolidColorBrush x:Key="SecondaryButtonBackgroundBrush" Color="#404040"/>
                    <SolidColorBrush x:Key="ButtonTextBrush" Color="#FFFFFF"/>
                    <SolidColorBrush x:Key="UserMessageBackgroundBrush" Color="#0078D4"/>
                    <SolidColorBrush x:Key="UserMessageTextBrush" Color="#FFFFFF"/>
                    <SolidColorBrush x:Key="AiMessageBackgroundBrush" Color="#3C3C3C"/>
                    <SolidColorBrush x:Key="AiMessageTextBrush" Color="#E0E0E0"/>
                    
                    <!-- ThinkBlock颜色 -->
                    <SolidColorBrush x:Key="ThinkBlockBackgroundBrush" Color="#2A2A2C"/>
                    <SolidColorBrush x:Key="ThinkBlockBorderBrush" Color="#404040"/>
                    <SolidColorBrush x:Key="ThinkBlockTitleBrush" Color="#C0C0C0"/>
                    <SolidColorBrush x:Key="ThinkBlockIconBrush" Color="#B0B0B0"/>
                    <SolidColorBrush x:Key="ThinkBlockPreviewBrush" Color="#909090"/>
                    <SolidColorBrush x:Key="ThinkBlockContentBackgroundBrush" Color="#222224"/>
                    <SolidColorBrush x:Key="ThinkBlockContentBorderBrush" Color="#383838"/>
                    <SolidColorBrush x:Key="ThinkBlockContentTextBrush" Color="#D0D0D0"/>
                    
                    <!-- 文本选择颜色 -->
                    <SolidColorBrush x:Key="TextSelectionHighlightBrush" Color="#FF0078D4"/>
                    <SolidColorBrush x:Key="InactiveSelectionHighlightBrush" Color="#80404040"/>
                    
                    <!-- 代码块主题颜色 -->
                    <SolidColorBrush x:Key="CodeBlockBackgroundBrush" Color="#1E1E1E"/>
                    <SolidColorBrush x:Key="CodeBlockBorderBrush" Color="#404040"/>
                    <SolidColorBrush x:Key="CodeBlockTextBrush" Color="#D4D4D4"/>
                    <SolidColorBrush x:Key="CodeBlockHeaderBrush" Color="#2D2D30"/>
                    
                    <!-- 复制按钮颜色 -->
                    <SolidColorBrush x:Key="CopyButtonBackgroundBrush" Color="#404040"/>
                    <SolidColorBrush x:Key="CopyButtonHoverBrush" Color="#505050"/>
                    <SolidColorBrush x:Key="CopyButtonTextBrush" Color="#E0E0E0"/>
                    <SolidColorBrush x:Key="CopySuccessBrush" Color="#4CAF50"/>
                    
                    <!-- 侧边栏颜色 -->
                    <SolidColorBrush x:Key="SidebarBackgroundBrush" Color="#252526"/>
                    <SolidColorBrush x:Key="SidebarBorderBrush" Color="#404040"/>
                    <SolidColorBrush x:Key="SidebarHeaderBackgroundBrush" Color="#2D2D30"/>
                    <SolidColorBrush x:Key="SessionItemHoverBackgroundBrush" Color="#404040"/>
                    <SolidColorBrush x:Key="SessionItemSelectedBackgroundBrush" Color="#0078D4"/>
                    <SolidColorBrush x:Key="SessionItemBorderBrush" Color="#404040"/>
                    <SolidColorBrush x:Key="SessionItemHoverBorderBrush" Color="#505050"/>
                    <SolidColorBrush x:Key="SessionItemSelectedBorderBrush" Color="#0078D4"/>
                    <SolidColorBrush x:Key="SearchBoxBackgroundBrush" Color="#3C3C3C"/>
                    <SolidColorBrush x:Key="SearchBoxBorderBrush" Color="#505050"/>
                    <SolidColorBrush x:Key="TertiaryTextBrush" Color="#808080"/>
                </ResourceDictionary>

                <!-- 浅色主题 -->
                <ResourceDictionary x:Key="Light">
                    <!-- 背景颜色 -->
                    <SolidColorBrush x:Key="AppBackgroundBrush" Color="#FFFFFF"/>
                    <Color x:Key="AppBackgroundGradientStartColor">#FFFFFF</Color>
                    <Color x:Key="AppBackgroundGradientEndColor">#F5F5F5</Color>

                    <!-- 卡片/容器背景 -->
                    <SolidColorBrush x:Key="CardBackgroundBrush" Color="#F8F8F8"/>
                    <SolidColorBrush x:Key="CardBorderBrush" Color="#E0E0E0"/>

                    <!-- 按钮颜色 -->
                    <SolidColorBrush x:Key="ButtonBackgroundBrush" Color="#F8F8F8"/>
                    <Color x:Key="ButtonBackgroundGradientStartColor">#F8F8F8</Color>
                    <Color x:Key="ButtonBackgroundGradientEndColor">#E8E8E8</Color>
                    <SolidColorBrush x:Key="ButtonBorderBrush" Color="#D0D0D0"/>
                    <Color x:Key="ButtonBorderGradientStartColor">#D0D0D0</Color>
                    <Color x:Key="ButtonBorderGradientEndColor">#C0C0C0</Color>

                    <!-- 按钮悬停状态 -->
                    <SolidColorBrush x:Key="ButtonHoverBackgroundBrush" Color="#E8E8E8"/>
                    <Color x:Key="ButtonHoverGradientStartColor">#E8E8E8</Color>
                    <Color x:Key="ButtonHoverGradientEndColor">#D8D8D8</Color>
                    <Color x:Key="ButtonHoverBorderGradientStartColor">#C0C0C0</Color>
                    <Color x:Key="ButtonHoverBorderGradientEndColor">#B0B0B0</Color>

                    <!-- 按钮按下状态 - 使用主题色的深色变体 -->
                    <Color x:Key="ButtonPressedGradientStartColor">#D6E8F5</Color>
                    <Color x:Key="ButtonPressedGradientEndColor">#C2D9ED</Color>

                    <!-- 半球按钮立体效果颜色 -->
                    <Color x:Key="ButtonShadowColor">#C0C0C0</Color>
                    <Color x:Key="ButtonDeepShadowColor">#A0A0A0</Color>
                    <Color x:Key="ButtonHighlightColor">#50FFFFFF</Color>
                    <Color x:Key="ButtonMidHighlightColor">#25FFFFFF</Color>

                    <!-- 3D立体按钮专用颜色 -->
                    <!-- 按钮顶面 - 最亮面，接受光照 -->
                    <Color x:Key="Button3DTopStartColor">#4A5568</Color>
                    <Color x:Key="Button3DTopMidColor">#3A4556</Color>
                    <Color x:Key="Button3DTopEndColor">#2D3748</Color>
                    
                    <!-- 按钮侧面 - 中等亮度，营造厚度感 -->
                    <Color x:Key="Button3DSideStartColor">#2D3748</Color>
                    <Color x:Key="Button3DSideEndColor">#1A202C</Color>
                    
                    <!-- 按钮底面/阴影 - 最暗面 -->
                    <Color x:Key="Button3DBottomColor">#0F1419</Color>
                    <Color x:Key="Button3DDeepShadowColor">#0A0E13</Color>
                    
                    <!-- 3D按钮高光系统 -->
                    <Color x:Key="Button3DHighlightTopColor">#80FFFFFF</Color>
                    <Color x:Key="Button3DHighlightMidColor">#40FFFFFF</Color>
                    <Color x:Key="Button3DHighlightEdgeColor">#20FFFFFF</Color>
                    
                    <!-- 3D按钮边框 -->
                    <Color x:Key="Button3DBorderTopColor">#5A6B7D</Color>
                    <Color x:Key="Button3DBorderBottomColor">#1A202C</Color>
                    
                    <!-- 悬停状态 -->
                    <Color x:Key="Button3DHoverTopStartColor">#5A6B7D</Color>
                    <Color x:Key="Button3DHoverTopMidColor">#4A5A6D</Color>
                    <Color x:Key="Button3DHoverTopEndColor">#3A4A5D</Color>
                    
                    <!-- 按下状态 -->
                    <Color x:Key="Button3DPressedTopColor">#2A3441</Color>
                    <Color x:Key="Button3DPressedBottomColor">#1A2431</Color>

                    <!-- 文本颜色 -->
                    <SolidColorBrush x:Key="PrimaryTextBrush" Color="#2D2D30"/>
                    <SolidColorBrush x:Key="SecondaryTextBrush" Color="#666666"/>
                    <SolidColorBrush x:Key="AccentTextBrush" Color="#000000"/>

                    <!-- 图标颜色 -->
                    <SolidColorBrush x:Key="IconBrush" Color="#2D2D30"/>
                    <SolidColorBrush x:Key="SecondaryIconBrush" Color="#666666"/>

                    <!-- 浮动面板 -->
                    <SolidColorBrush x:Key="FloatingPanelBackgroundBrush" Color="#CCF8F8F8"/>
                    <SolidColorBrush x:Key="FloatingPanelBorderBrush" Color="#E0E0E0"/>

                    <!-- 欢迎界面背景 -->
                    <Color x:Key="WelcomeBackgroundStartColor">#F0F0F0</Color>
                    <Color x:Key="WelcomeBackgroundEndColor">#E0E0E0</Color>

                    <!-- 光晕效果颜色 -->
                    <Color x:Key="GlowPrimaryColor">#2E86AB</Color>
                    <Color x:Key="GlowSecondaryColor">#A23B72</Color>
                    <Color x:Key="GlowAccentColor">#F18F01</Color>
                    <SolidColorBrush x:Key="GlowPrimaryBrush" Color="{DynamicResource GlowPrimaryColor}"/>
                    <SolidColorBrush x:Key="GlowSecondaryBrush" Color="{DynamicResource GlowSecondaryColor}"/>
                    <SolidColorBrush x:Key="GlowAccentBrush" Color="{DynamicResource GlowAccentColor}"/>

                    <!-- 词云文字颜色 -->
                    <SolidColorBrush x:Key="WordCloudTextBrush" Color="#60000000"/>

                    <!-- 对话界面颜色 -->
                    <SolidColorBrush x:Key="ChatBackgroundBrush" Color="#FAFAFA"/>
                    <SolidColorBrush x:Key="ChatHeaderBackgroundBrush" Color="#F0F0F0"/>
                    <SolidColorBrush x:Key="ChatInputBackgroundBrush" Color="#F0F0F0"/>
                    <SolidColorBrush x:Key="PrimaryButtonBackgroundBrush" Color="#0078D4"/>
                    <SolidColorBrush x:Key="SecondaryButtonBackgroundBrush" Color="#E0E0E0"/>
                    <SolidColorBrush x:Key="ButtonTextBrush" Color="#FFFFFF"/>
                    <SolidColorBrush x:Key="UserMessageBackgroundBrush" Color="#0078D4"/>
                    <SolidColorBrush x:Key="UserMessageTextBrush" Color="#FFFFFF"/>
                    <SolidColorBrush x:Key="AiMessageBackgroundBrush" Color="#E8E8E8"/>
                    <SolidColorBrush x:Key="AiMessageTextBrush" Color="#2D2D30"/>
                    
                    <!-- ThinkBlock颜色 -->
                    <SolidColorBrush x:Key="ThinkBlockBackgroundBrush" Color="#F5F5F7"/>
                    <SolidColorBrush x:Key="ThinkBlockBorderBrush" Color="#D0D0D0"/>
                    <SolidColorBrush x:Key="ThinkBlockTitleBrush" Color="#404040"/>
                    <SolidColorBrush x:Key="ThinkBlockIconBrush" Color="#606060"/>
                    <SolidColorBrush x:Key="ThinkBlockPreviewBrush" Color="#808080"/>
                    <SolidColorBrush x:Key="ThinkBlockContentBackgroundBrush" Color="#FFFFFF"/>
                    <SolidColorBrush x:Key="ThinkBlockContentBorderBrush" Color="#E0E0E0"/>
                    <SolidColorBrush x:Key="ThinkBlockContentTextBrush" Color="#2D2D30"/>
                    
                    <!-- 文本选择颜色 -->
                    <SolidColorBrush x:Key="TextSelectionHighlightBrush" Color="#FF0078D4"/>
                    <SolidColorBrush x:Key="InactiveSelectionHighlightBrush" Color="#80D0D0D0"/>
                    
                    <!-- 代码块主题颜色 -->
                    <SolidColorBrush x:Key="CodeBlockBackgroundBrush" Color="#F8F8F8"/>
                    <SolidColorBrush x:Key="CodeBlockBorderBrush" Color="#E1E4E8"/>
                    <SolidColorBrush x:Key="CodeBlockTextBrush" Color="#24292E"/>
                    <SolidColorBrush x:Key="CodeBlockHeaderBrush" Color="#F0F0F0"/>
                    
                    <!-- 复制按钮颜色 -->
                    <SolidColorBrush x:Key="CopyButtonBackgroundBrush" Color="#E0E0E0"/>
                    <SolidColorBrush x:Key="CopyButtonHoverBrush" Color="#D0D0D0"/>
                    <SolidColorBrush x:Key="CopyButtonTextBrush" Color="#2D2D30"/>
                    <SolidColorBrush x:Key="CopySuccessBrush" Color="#4CAF50"/>
                    
                    <!-- 侧边栏颜色 -->
                    <SolidColorBrush x:Key="SidebarBackgroundBrush" Color="#FAFAFA"/>
                    <SolidColorBrush x:Key="SidebarBorderBrush" Color="#E0E0E0"/>
                    <SolidColorBrush x:Key="SidebarHeaderBackgroundBrush" Color="#F0F0F0"/>
                    <SolidColorBrush x:Key="SessionItemHoverBackgroundBrush" Color="#F0F0F0"/>
                    <SolidColorBrush x:Key="SessionItemSelectedBackgroundBrush" Color="#0078D4"/>
                    <SolidColorBrush x:Key="SessionItemBorderBrush" Color="#E0E0E0"/>
                    <SolidColorBrush x:Key="SessionItemHoverBorderBrush" Color="#D0D0D0"/>
                    <SolidColorBrush x:Key="SessionItemSelectedBorderBrush" Color="#0078D4"/>
                    <SolidColorBrush x:Key="SearchBoxBackgroundBrush" Color="#FFFFFF"/>
                    <SolidColorBrush x:Key="SearchBoxBorderBrush" Color="#D0D0D0"/>
                    <SolidColorBrush x:Key="TertiaryTextBrush" Color="#666666"/>
                </ResourceDictionary>
            </ResourceDictionary.ThemeDictionaries>
        </ResourceDictionary>
    </Application.Resources>
</Application>