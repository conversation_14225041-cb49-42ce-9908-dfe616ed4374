<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:materialIcons="clr-namespace:Material.Icons.Avalonia;assembly=Material.Icons.Avalonia"
             mc:Ignorable="d" d:DesignWidth="400" d:DesignHeight="200"
             x:Class="Lyxie_desktop.Controls.CodeBlockControl"
             Background="Transparent">

    <Border Name="CodeBlockBorder"
            Background="{DynamicResource CodeBlockBackgroundBrush}"
            BorderBrush="{DynamicResource CodeBlockBorderBrush}"
            BorderThickness="1"
            CornerRadius="4"
            Margin="0,0,0,8">
        
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>
            
            <!-- 代码块头部 -->
            <Border Grid.Row="0"
                    Name="CodeBlockHeader"
                    Background="{DynamicResource CodeBlockHeaderBrush}"
                    CornerRadius="4,4,0,0"
                    BorderThickness="0,0,0,1"
                    BorderBrush="{DynamicResource CodeBlockBorderBrush}"
                    Padding="12,8"
                    IsVisible="False">
                
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <!-- 语言标签 -->
                    <TextBlock Grid.Column="0"
                               Name="LanguageLabel"
                               Text="Code"
                               FontSize="12"
                               FontFamily="Consolas, 'Courier New', monospace"
                               Foreground="{DynamicResource SecondaryTextBrush}"
                               VerticalAlignment="Center"/>
                    
                    <!-- 复制按钮 -->
                    <Button Grid.Column="1"
                            Name="CopyButton"
                            Width="32"
                            Height="24"
                            Background="{DynamicResource CopyButtonBackgroundBrush}"
                            BorderThickness="0"
                            CornerRadius="3"
                            Cursor="Hand"
                            ToolTip.Tip="复制代码">
                        <materialIcons:MaterialIcon Kind="ContentCopy"
                                                    Width="14"
                                                    Height="14"
                                                    Foreground="{DynamicResource CopyButtonTextBrush}"/>
                        
                        <Button.Styles>
                            <Style Selector="Button:pointerover">
                                <Setter Property="Background" Value="{DynamicResource CopyButtonHoverBrush}"/>
                            </Style>
                        </Button.Styles>
                    </Button>
                </Grid>
            </Border>
            
            <!-- 代码内容区域 -->
            <Border Grid.Row="1"
                    Padding="12">
                <Grid>
                    <!-- 代码文本 -->
                    <SelectableTextBlock Name="CodeText"
                                         FontFamily="Consolas, 'Courier New', monospace"
                                         FontSize="13"
                                         TextWrapping="Wrap"
                                         Foreground="{DynamicResource CodeBlockTextBrush}"
                                         SelectionBrush="{DynamicResource TextSelectionHighlightBrush}"
                                         Background="Transparent"/>
                    
                    <!-- 浮动复制按钮 -->
                    <Button Name="FloatingCopyButton"
                            Width="32"
                            Height="32"
                            HorizontalAlignment="Right"
                            VerticalAlignment="Top"
                            Margin="0,4,4,0"
                            Background="{DynamicResource CopyButtonBackgroundBrush}"
                            BorderThickness="0"
                            CornerRadius="4"
                            Cursor="Hand"
                            Opacity="0.7"
                            ToolTip.Tip="复制代码">
                        
                        <Grid>
                            <!-- 默认复制图标 -->
                            <materialIcons:MaterialIcon Name="CopyIcon"
                                                        Kind="ContentCopy"
                                                        Width="16"
                                                        Height="16"
                                                        Foreground="{DynamicResource CopyButtonTextBrush}"/>
                            
                            <!-- 成功图标 -->
                            <materialIcons:MaterialIcon Name="SuccessIcon"
                                                        Kind="Check"
                                                        Width="16"
                                                        Height="16"
                                                        Foreground="{DynamicResource CopySuccessBrush}"
                                                        IsVisible="False"/>
                        </Grid>
                        
                        <Button.Styles>
                            <Style Selector="Button:pointerover">
                                <Setter Property="Background" Value="{DynamicResource CopyButtonHoverBrush}"/>
                                <Setter Property="Opacity" Value="1.0"/>
                            </Style>
                        </Button.Styles>
                    </Button>
                </Grid>
            </Border>
        </Grid>
    </Border>

</UserControl> 