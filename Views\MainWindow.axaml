<Window xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:views="clr-namespace:Lyxie_desktop.Views"
        mc:Ignorable="d" d:DesignWidth="1280" d:DesignHeight="760"
        x:Class="Lyxie_desktop.Views.MainWindow"
        Title="Lyxie"
        Width="1280" Height="760"
        MinWidth="1280" MinHeight="760"
        WindowStartupLocation="CenterScreen">

    <Grid ClipToBounds="True">
        <!-- 欢迎界面 -->
        <views:WelcomeView Name="WelcomeView"
                           ClipToBounds="True">
            <views:WelcomeView.RenderTransform>
                <TranslateTransform X="0" Y="0"/>
            </views:WelcomeView.RenderTransform>
        </views:WelcomeView>

        <!-- 主界面 (初始位置在下方) -->
        <views:MainView Name="MainView"
                        ClipToBounds="True"
                        ZIndex="1">
            <views:MainView.RenderTransform>
                <TranslateTransform X="0" Y="760"/>
            </views:MainView.RenderTransform>
        </views:MainView>

        <!-- 设置界面 (初始位置在左侧，Z-Index更高以遮挡主界面) -->
        <views:SettingsView Name="SettingsView"
                            ClipToBounds="True"
                            ZIndex="2"
                            IsVisible="True">
            <views:SettingsView.RenderTransform>
                <TranslateTransform X="-1280" Y="0"/>
            </views:SettingsView.RenderTransform>
        </views:SettingsView>
    </Grid>

</Window>
