<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             mc:Ignorable="d" d:DesignWidth="1280" d:DesignHeight="760"
             x:Class="Lyxie_desktop.Views.WelcomeView">

    <Grid>
        <!-- 现代渐变背景 -->
        <Grid.Background>
            <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                <GradientStop Color="{DynamicResource WelcomeBackgroundStartColor}" Offset="0"/>
                <GradientStop Color="{DynamicResource WelcomeBackgroundEndColor}" Offset="1"/>
            </LinearGradientBrush>
        </Grid.Background>

        <StackPanel HorizontalAlignment="Center"
                    VerticalAlignment="Center">
            <TextBlock Name="WelcomeTextBlock"
                       Text="欢迎使用 Lyxie"
                       FontSize="36"
                       FontWeight="Light"
                       Foreground="{DynamicResource AccentTextBrush}"
                       HorizontalAlignment="Center"
                       Margin="0,0,0,30"
                       Opacity="0.95"/>

            <TextBlock Name="StartingTextBlock"
                       Text="正在启动..."
                       FontSize="18"
                       FontWeight="Light"
                       Foreground="{DynamicResource SecondaryTextBrush}"
                       HorizontalAlignment="Center"
                       Opacity="0.7"/>
        </StackPanel>
    </Grid>

</UserControl>
